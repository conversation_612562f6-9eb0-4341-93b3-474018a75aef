from Plugins._Tools import Tools
from Plugins._Tools.ReferenceMessageHandler import ReferenceMessageHandler
from Plugins._Tools.ReferenceMessageErrorHandler import ReferenceMessageErrorHandler
from Config.logger import logger
from Core.PluginBase import PluginBase
import os
import json
import time
import uuid
import httpx
import asyncio
import base64
from typing import Optional, Dict, Any

class DoubaoImageGenerationPlugin(PluginBase, ReferenceMessageHandler):
    def __init__(self):
        super().__init__()
        self.name = "DoubaoImageGeneration"
        self.description = "豆包图生图插件"
        self.version = "1.0.0"
        self.author = "XYBot"

        # 工具类实例
        self.tools = Tools()

        # 加载配置文件
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.api_config = self.configData.get('API', {})

        # 豆包API配置
        self.api_base_url = "https://www.doubao.com/samantha/chat/completion"
        self.cookies = self.api_config.get('cookies', '')

        # 豆包API常量
        self.device_id = "7468716989062841895"
        self.tea_uuid = "7468716986638386703"
        self.web_id = "7468716986638386703"

        # 豆包图生图：引用图片+豆包+任一提示词
        self.generation_keywords = ["豆包"]

        # 图片缓存
        self.image_cache = {}

        # httpx客户端管理
        self._client = None
        self._client_lock = asyncio.Lock()

    async def get_session(self):
        """获取httpx客户端"""
        async with self._client_lock:
            if self._client is None or self._client.is_closed:
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Accept": "text/event-stream",
                    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Connection": "keep-alive",
                    "Cookie": self.cookies,
                    "Referer": "https://www.doubao.com/chat/",
                    "Origin": "https://www.doubao.com",
                    "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                    "Sec-Ch-Ua-Mobile": "?0",
                    "Sec-Ch-Ua-Platform": '"Windows"',
                    "Sec-Fetch-Site": "same-origin",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Dest": "empty"
                }
                self._client = httpx.AsyncClient(
                    headers=headers,
                    timeout=httpx.Timeout(30.0, connect=10.0),
                    follow_redirects=True
                )
            return self._client

    async def handle_message(self, msg) -> bool:
        """处理消息"""
        try:
            # 如果是图片消息，缓存图片信息
            if msg.type == 3:  # 图片消息
                await self._cache_image_info(msg)
                return False

            # 检查是否是图生图请求
            if not self._is_image_generation_request(msg):
                return False

            # 处理图生图请求
            await self._handle_image_generation_request(msg)
            return True

        except Exception as e:
            logger.error(f"插件 {self.name} 处理消息时出错: {e}")
            return False

    def _is_image_generation_request(self, msg) -> bool:
        """判断是否是图生图请求：引用图片+豆包+任一提示词"""
        # 只处理引用消息
        if msg.type != 49:
            return False

        # 使用标准方法检查是否是引用消息
        if not self._is_reference_message(msg.content):
            return False

        # 检查引用的是否是图片
        if self._get_reference_type(msg.content) != "3":
            return False

        # 提取用户指令
        prompt = self._extract_prompt_from_reference(msg.content)
        if not prompt:
            return False

        # 检查是否包含豆包关键词
        return prompt.startswith("豆包")

    def _has_available_image(self, msg) -> bool:
        """检查是否有可用的图片"""
        # 检查消息是否包含引用信息
        if hasattr(msg, 'content') and msg.content:
            if 'refermsg' in msg.content:
                return True

        # 检查最近的图片缓存
        target_id = msg.roomid if msg.from_group() else msg.sender
        current_time = time.time()

        # 查找最近5分钟内的图片
        for info in self.image_cache.values():
            if (info.get("to_wxid") == target_id and
                current_time - info.get("timestamp", 0) < 300):
                return True

        return False

    async def _cache_image_info(self, msg):
        """缓存图片消息的下载信息"""
        try:
            msg_id = str(msg.id)
            self.image_cache[msg_id] = {
                "msg_id": msg_id,
                "to_wxid": msg.roomid if msg.from_group() else msg.sender,
                "wxid": msg.self_wxid,
                "timestamp": time.time(),
                "content": msg.content
            }

            # 清理过期缓存（保留最近1小时的图片）
            current_time = time.time()
            expired_keys = [
                key for key, info in self.image_cache.items()
                if current_time - info["timestamp"] > 3600
            ]
            for key in expired_keys:
                del self.image_cache[key]

        except Exception as e:
            logger.error(f"缓存图片信息失败: {e}")

    async def _handle_image_generation_request(self, msg):
        """处理图生图请求"""
        try:
            target_id = msg.roomid if msg.from_group() else msg.sender

            # 使用标准方法获取图片数据
            image_data = await self._get_reference_image(msg.content, msg.self_wxid)
            prompt = self._extract_prompt_from_message(msg)

            if not image_data:
                await ReferenceMessageErrorHandler.handle_image_not_found(
                    self.dp, target_id, msg.self_wxid
                )
                return

            # 调用豆包API生成图片
            result = await self.generate_image(image_data, prompt)

            if result and result.get("success"):
                # 先发送文本回复（如果有）
                text_reply = result.get("text_reply", "").strip()
                if text_reply:
                    await self.dp.sendText(text_reply, target_id, msg.self_wxid)

                # 再发送生成的图片
                image_url = result.get("image_url")
                if image_url:
                    await self.dp.sendImage(image_url, target_id, msg.self_wxid)
                else:
                    await self.dp.sendText("图片生成完成，但获取图片失败", target_id, msg.self_wxid)
            else:
                error_msg = result.get("error", "图片生成失败") if result else "图片生成失败"
                await self.dp.sendText(f"❌ {error_msg}", target_id, msg.self_wxid)

        except Exception as e:
            logger.error(f"处理图生图请求失败: {e}")
            await ReferenceMessageErrorHandler.handle_processing_error(
                self.dp, target_id, msg.self_wxid, "图片生成"
            )

    def _extract_prompt_from_message(self, msg) -> str:
        """从引用消息中提取提示词：豆包+任一提示词"""
        # 使用标准方法提取提示词
        prompt = self._extract_prompt_from_reference(msg.content)
        if prompt and prompt.startswith("豆包"):
            # 移除"豆包"前缀，保留后面的提示词
            cleaned_prompt = prompt[2:].strip()
            return cleaned_prompt if cleaned_prompt else "请根据这张图片生成一张新的图片"

        return "请根据这张图片生成一张新的图片"

    async def _get_recent_image(self, msg) -> Optional[bytes]:
        """获取最近的图片数据"""
        try:
            target_id = msg.roomid if msg.from_group() else msg.sender
            current_time = time.time()

            # 查找最近5分钟内的图片
            recent_image = None
            latest_time = 0

            for info in self.image_cache.values():
                if (info.get("to_wxid") == target_id and
                    current_time - info.get("timestamp", 0) < 300 and
                    info.get("timestamp", 0) > latest_time):
                    recent_image = info
                    latest_time = info.get("timestamp", 0)

            if recent_image:
                return await self._get_image_from_cache(recent_image)

            return None

        except Exception as e:
            logger.error(f"获取最近图片失败: {e}")
            return None

    async def _get_image_from_cache(self, image_info: Dict) -> Optional[bytes]:
        """从缓存信息中获取图片数据"""
        try:
            content = image_info.get("content", "")
            if content:
                # 检查是否是URL
                if content.startswith('http'):
                    client = await self.get_session()
                    # 添加重试机制
                    for attempt in range(3):
                        try:
                            response = await client.get(content)
                            if response.status_code == 200:
                                return response.content
                            break
                        except (httpx.TimeoutException, httpx.ConnectError) as e:
                            if attempt < 2:  # 最多重试2次
                                await asyncio.sleep(0.5)
                                continue
                            else:
                                logger.warning(f"图片下载失败: {e}")
                                break

                # 检查是否是本地文件路径
                elif os.path.exists(content):
                    with open(content, 'rb') as f:
                        return f.read()

            return None

        except Exception as e:
            logger.error(f"从缓存获取图片数据失败: {e}")
            return None





    async def generate_image(self, image_data: bytes, prompt: str) -> Dict[str, Any]:
        """调用豆包API生成图片"""
        try:
            # 上传图片获取image_uri
            image_uri = await self._upload_image(image_data)
            if not image_uri:
                return {"success": False, "error": "图片上传失败"}

            # 构建请求参数
            base_params = {
                'version_code': '20800',
                'language': 'zh',
                'device_platform': 'web',
                'aid': '497858',
                'real_aid': '497858',
                'pkg_type': 'release_version',
                'device_id': self.device_id,
                'web_id': self.web_id,
                'tea_uuid': self.tea_uuid,
                'use-olympus-account': '1',
                'region': 'CN',
                'sys_region': 'CN',
                'samantha_web': '1',
                'pc_version': '2.24.2'
            }

            # 构建请求数据
            request_data = {
                "messages": [{
                    "content": json.dumps({"text": prompt + "！"}),
                    "content_type": 2009,
                    "attachments": [{
                        "type": "image",
                        "key": image_uri,
                        "extra": {
                            "refer_types": "overall"
                        },
                        "identifier": str(uuid.uuid4())
                    }],
                    "references": []
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "use_auto_cot": False,
                    "resend_for_regen": False,
                    "event_id": "0"
                },
                "conversation_id": "0",
                "local_conversation_id": f"local_{int(time.time() * 1000000)}",
                "local_message_id": str(uuid.uuid4())
            }

            # 构建完整URL
            from urllib.parse import urlencode
            api_url = f"{self.api_base_url}?{urlencode(base_params)}"

            # 发送请求
            client = await self.get_session()

            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/plain, */*',
                'x-flow-trace': f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01",
                'Agw-Js-Conv': 'str',
                'last-event-id': 'undefined',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty'
            }

            # 添加重试机制
            max_retries = 2
            for attempt in range(max_retries + 1):
                try:
                    response = await client.post(api_url, json=request_data, headers=headers)
                    break
                except (httpx.TimeoutException, httpx.ConnectError) as e:
                    if attempt < max_retries:
                        await asyncio.sleep(1)  # 短暂等待后重试
                        continue
                    else:
                        return {"success": False, "error": f"网络请求失败: {str(e)}"}

            if response.status_code != 200:
                return {"success": False, "error": f"API请求失败: {response.status_code}"}

            # 解析流式响应
            generated_images = []
            text_replies = []

            try:
                async for line in response.aiter_lines():
                    if not line.startswith("data: "):
                        continue

                    data_str = line[6:].strip()
                    if not data_str:
                        continue

                    try:
                        event_data = json.loads(data_str)
                        if 'event_data' not in event_data:
                            continue

                        inner_data = json.loads(event_data['event_data'])

                        # 检查错误
                        error_code = inner_data.get('code')
                        if error_code and str(error_code).startswith('71002'):
                            return {"success": False, "error": f"请求被限制: {error_code}"}

                        # 解析图片生成结果
                        if 'message' not in inner_data or 'content' not in inner_data['message']:
                            continue

                        message = inner_data['message']
                        content_type = message.get('content_type', 0)

                        try:
                            content = json.loads(message['content'])
                        except json.JSONDecodeError:
                            continue

                        # 收集文本回复 (content_type: 10000)
                        if content_type == 10000 and 'text' in content:
                            text_content = content['text'].strip()
                            if text_content:
                                text_replies.append(text_content)

                        # 查找图片信息 (content_type: 2074)
                        if 'image_info' in content:
                            image_info = content['image_info']
                            status = image_info.get('status', 0)

                            if status == 2:  # 生成完成
                                urls = {
                                    'original': image_info.get('image_ori', {}).get('url'),
                                    'raw': image_info.get('image_raw', {}).get('url'),
                                    'preview': image_info.get('preview_img', {}).get('url')
                                }

                                valid_urls = {k: v for k, v in urls.items() if v}
                                if valid_urls:
                                    # 优先返回原图，其次是raw，最后是其他
                                    if urls['original']:
                                        generated_images.append(urls['original'])
                                    elif urls['raw']:
                                        generated_images.append(urls['raw'])
                                    else:
                                        final_url = list(valid_urls.values())[0]
                                        generated_images.append(final_url)
                            elif status == 3:  # 生成失败
                                return {"success": False, "error": "图片生成失败"}

                        elif 'creations' in content:
                            # 新的响应结构：使用creations数组
                            creations = content['creations']

                            for creation in creations:
                                if creation.get('type') == 1 and 'image' in creation:
                                    image_info = creation['image']
                                    status = image_info.get('status', 0)

                                    if status == 2:  # 生成完成
                                        # 提取各种格式的图片URL
                                        urls = {}
                                        if 'image_ori' in image_info and 'url' in image_info['image_ori']:
                                            urls['original'] = image_info['image_ori']['url']
                                        if 'image_raw' in image_info and 'url' in image_info['image_raw']:
                                            urls['raw'] = image_info['image_raw']['url']
                                        if 'preview_img' in image_info and 'url' in image_info['preview_img']:
                                            urls['preview'] = image_info['preview_img']['url']

                                        if urls:
                                            # 根据用户偏好返回高清图片（优先级：原图 > raw > preview > 其他）
                                            if 'original' in urls:
                                                final_image_url = urls['original']
                                            elif 'raw' in urls:
                                                final_image_url = urls['raw']
                                            elif 'preview' in urls:
                                                final_image_url = urls['preview']
                                            else:
                                                final_image_url = list(urls.values())[0]

                                            generated_images.append(final_image_url)
                                    elif status == 3:  # 生成失败
                                        return {"success": False, "error": "图片生成失败"}

                    except json.JSONDecodeError:
                        continue
                    except Exception as e:
                        logger.warning(f"解析响应数据失败: {e}")
                        continue
            except Exception as e:
                logger.error(f"处理流式响应失败: {e}")
                return {"success": False, "error": f"响应处理失败: {str(e)}"}

            # 合并所有文本回复
            text_reply = "".join(text_replies) if text_replies else ""

            if generated_images:
                return {
                    "success": True,
                    "image_url": generated_images[0],
                    "text_reply": text_reply
                }
            else:
                return {
                    "success": False,
                    "error": "未找到生成的图片",
                    "text_reply": text_reply
                }

        except Exception as e:
            logger.error(f"调用豆包API生成图片失败: {e}")
            return {"success": False, "error": str(e)}

    async def _upload_image(self, image_data: bytes) -> Optional[str]:
        """上传图片到豆包服务器"""
        return await self.upload_image_aws(image_data)

    def _parse_cookies(self, cookie_string: str) -> Dict[str, str]:
        """解析Cookie字符串"""
        cookies = {}
        for item in cookie_string.split(';'):
            if '=' in item:
                key, value = item.strip().split('=', 1)
                cookies[key] = value
        return cookies

    def _calculate_crc32(self, data: bytes) -> int:
        """计算文件CRC32校验值"""
        import zlib
        return zlib.crc32(data) & 0xffffffff

    def _generate_aws_signature(self, method: str, url: str, upload_auth: Dict, payload: str = "") -> Dict[str, str]:
        """生成AWS4-HMAC-SHA256签名"""
        import hashlib
        import hmac
        import datetime as dt
        from urllib.parse import urlparse

        access_key = upload_auth['access_key']
        secret_key = upload_auth['secret_key']
        session_token = upload_auth['session_token']

        # 解析URL
        parsed = urlparse(url)
        host = parsed.netloc
        path = parsed.path if parsed.path else '/'
        query = parsed.query

        # 时间戳
        t = dt.datetime.now(dt.timezone.utc)
        amz_date = t.strftime('%Y%m%dT%H%M%SZ')
        date_stamp = t.strftime('%Y%m%d')

        # 规范化查询字符串
        if query:
            query_params = []
            for param in query.split('&'):
                if '=' in param:
                    key, value = param.split('=', 1)
                    query_params.append((key, value))
                else:
                    query_params.append((param, ''))
            query_params.sort()
            canonical_querystring = '&'.join([f'{k}={v}' for k, v in query_params])
        else:
            canonical_querystring = ''

        # 规范化头部
        canonical_headers = f'host:{host}\nx-amz-date:{amz_date}\nx-amz-security-token:{session_token}\n'
        signed_headers = 'host;x-amz-date;x-amz-security-token'

        # 载荷哈希
        payload_hash = hashlib.sha256(payload.encode('utf-8')).hexdigest()

        # 规范化请求
        canonical_request = f'{method}\n{path}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{payload_hash}'

        # 创建签名字符串
        algorithm = 'AWS4-HMAC-SHA256'
        credential_scope = f'{date_stamp}/cn-north-1/imagex/aws4_request'
        string_to_sign = f'{algorithm}\n{amz_date}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode()).hexdigest()}'

        # 计算签名
        def sign(key, msg):
            if isinstance(key, str):
                key = key.encode('utf-8')
            return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest()

        def get_signature_key(key, date_stamp, region_name, service_name):
            k_date = sign('AWS4' + key, date_stamp)
            k_region = sign(k_date, region_name)
            k_service = sign(k_region, service_name)
            k_signing = sign(k_service, 'aws4_request')
            return k_signing

        signing_key = get_signature_key(secret_key, date_stamp, 'cn-north-1', 'imagex')
        signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

        # 构建认证头部
        authorization_header = f'{algorithm} Credential={access_key}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}'

        return {
            'Authorization': authorization_header,
            'X-Amz-Date': amz_date,
            'x-amz-security-token': session_token
        }

    async def upload_image_aws(self, image_data: bytes) -> Optional[str]:
        """上传图片到豆包服务器"""
        try:
            # 使用httpx异步客户端，设置适当的超时时间
            cookies = self._parse_cookies(self.cookies)

            # 创建httpx客户端，为上传操作设置较长的超时时间
            timeout_config = httpx.Timeout(
                connect=10.0,    # 连接超时
                read=120.0,      # 读取超时（上传可能需要较长时间）
                write=120.0,     # 写入超时（上传文件）
                pool=10.0        # 连接池超时
            )

            async with httpx.AsyncClient(
                cookies=cookies,
                timeout=timeout_config,
                follow_redirects=True,
                verify=False
            ) as client:
                base_headers = {
                    'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
                    'Accept': '*/*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                    'Accept-Encoding': 'gzip, deflate',
                    'Origin': 'https://www.doubao.com',
                    'Referer': 'https://www.doubao.com/chat/create-image',
                    'X-Requested-With': 'mark.via',
                }

                service_id = "a9rns2rl98"
                file_size = len(image_data)
                file_ext = '.jpg'
                crc32_value = self._calculate_crc32(image_data)

                # 1. 获取上传认证信息
                url = "https://www.doubao.com/alice/resource/prepare_upload"
                params = {
                    'version_code': '20800',
                    'language': 'zh',
                    'device_platform': 'web',
                    'aid': '497858',
                    'real_aid': '497858',
                    'pkg_type': 'release_version',
                    'device_id': '7468716989062841895',
                    'web_id': '7468716986638386703',
                    'tea_uuid': '7468716986638386703',
                    'use-olympus-account': '1',
                    'region': 'CN',
                    'sys_region': 'CN',
                    'samantha_web': '1',
                    'pc_version': '2.24.2',
                }

                data = {
                    "tenant_id": "5",
                    "scene_id": "5",
                    "resource_type": 2
                }

                response = await client.post(url, params=params, json=data)
                if response.status_code != 200:
                    return None

                result = response.json()
                if result.get('code') != 0:
                    return None

                upload_auth = result['data']['upload_auth_token']

                # 2. 申请上传权限
                upload_params = {
                    'Action': 'ApplyImageUpload',
                    'Version': '2018-08-01',
                    'ServiceId': service_id,
                    'NeedFallback': 'true',
                    'FileSize': str(file_size),
                    'FileExtension': file_ext,
                    's': 'yy49d6n7o6p'
                }

                from urllib.parse import urlencode
                apply_url = f"https://imagex.bytedanceapi.com/?{urlencode(upload_params)}"

                # 3. 生成AWS签名
                aws_headers = self._generate_aws_signature('GET', apply_url, upload_auth)

                headers = base_headers.copy()
                headers.update(aws_headers)

                response = await client.get(apply_url, headers=headers)
                if response.status_code != 200:
                    return None

                upload_info = response.json()
                if 'Result' not in upload_info:
                    return None

                result = upload_info['Result']
                store_info = result['UploadAddress']['StoreInfos'][0]
                upload_host = result['UploadAddress']['UploadHosts'][0]

                store_uri = store_info['StoreUri']
                auth_token = store_info['Auth']
                upload_id = store_info['UploadID']

                # 4. 上传图片文件
                upload_url = f"https://{upload_host}/upload/v1/{store_uri}"

                upload_headers = {
                    'Authorization': auth_token,
                    'Content-CRC32': f"{crc32_value:08x}",
                    'Content-Type': 'application/octet-stream',
                    'X-Storage-U': upload_id,
                    'Origin': 'https://www.doubao.com',
                    'Referer': 'https://www.doubao.com/chat/',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                }

                response = await client.post(upload_url, content=image_data, headers=upload_headers)
                if response.status_code != 200:
                    return None

                upload_result = response.json()
                if upload_result.get('code') != 2000:
                    return None

                # 5. 提交上传完成
                commit_params = {
                    'Action': 'CommitImageUpload',
                    'Version': '2018-08-01',
                    'ServiceId': service_id
                }

                commit_url = f"https://imagex.bytedanceapi.com/?{urlencode(commit_params)}"

                session_key = result['UploadAddress']['SessionKey']
                commit_payload = json.dumps({"SessionKey": session_key})

                # 生成AWS签名
                commit_aws_headers = self._generate_aws_signature('POST', commit_url, upload_auth, commit_payload)

                commit_headers = {
                    'Content-Type': 'application/json',
                    'Accept': '*/*',
                    'Origin': 'https://www.doubao.com',
                    'Referer': 'https://www.doubao.com/chat/',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                }
                commit_headers.update(commit_aws_headers)

                response = await client.post(commit_url, content=commit_payload, headers=commit_headers)
                if response.status_code != 200:
                    return None

                commit_result = response.json()
                if 'Result' not in commit_result:
                    return None

                final_uri = commit_result['Result']['Results'][0]['Uri']
                return final_uri

        except Exception as e:
            logger.error(f"上传图片时发生错误: {e}")
            return None

    async def close(self):
        """插件关闭时的清理"""
        if self._client and not self._client.is_closed:
            await self._client.aclose()
