# 即梦AI绘画插件 (JiemengDraw)

一个功能强大的AI绘画插件，基于即梦API实现文字生图功能。

## 功能特性

- 🎨 **文字生图**：支持中文描述生成高质量图片
- 📐 **多种尺寸**：支持自定义图片比例和尺寸
- ⏰ **冷却限制**：防止频繁请求，保护API资源
- 🔄 **自动重试**：网络异常时自动重试机制
- 📱 **移动端优化**：使用APP端接口，稳定性更好

## 使用方法

### 基本用法

发送关键词 `即梦` 或 `jm` 加上描述文字即可生成图片：

```
即梦 美丽的风景画
jm 可爱的小猫咪
```

### 高级参数

#### 自定义比例

在描述后添加比例参数：

```
即梦 美女 16:9
即梦 风景 4:3
即梦 人物 1:1
```

#### 自定义尺寸

在描述后添加尺寸参数（256-2048像素）：

```
即梦 美女 1024
即梦 风景 512
即梦 人物 2048
```

#### 组合使用

可以同时指定比例和尺寸：

```
即梦 美女 16:9 1024
即梦 风景画 4:3 512
```

### 描述建议

- 尽可能详细描述场景、人物、动作、环境等
- 提示词越详细，生成效果越好
- 建议使用中文描述
- 可以添加艺术风格、光线效果等修饰词

### 示例

```
即梦 一位美丽的古装女子，穿着红色汉服，在樱花树下翩翩起舞，阳光透过花瓣洒在她身上，唯美梦幻的画面
jm 现代都市夜景，霓虹灯闪烁，高楼大厦林立，车水马龙，赛博朋克风格 16:9
即梦 可爱的橘猫，毛茸茸的，正在阳光下打盹，温馨治愈的画面 1:1 1024
```

## 配置说明

### 基本配置

- `enable`: 插件开关
- `command`: 触发关键词列表
- `cooldown`: 用户冷却时间（秒）

### API配置

- `device_id`: 设备ID
- `iid`: 实例ID

### 限流配置

- `tokens_per_second`: 令牌生成速率
- `bucket_size`: 令牌桶容量

## 注意事项

1. **冷却时间**：每个用户有30秒的冷却时间，防止频繁请求
2. **网络要求**：需要稳定的网络连接访问即梦API
3. **图片质量**：生成的图片质量取决于描述的详细程度
4. **API限制**：请合理使用，避免过度请求导致API限制

## 技术实现

- 使用即梦官方APP端API接口
- 支持异步请求和并发处理
- 自动处理图片下载和格式转换
- 完善的错误处理和重试机制

## 故障排除

### 常见问题

1. **生成失败**
   - 检查网络连接
   - 确认API配置正确
   - 尝试简化描述文字

2. **图片发送失败**
   - 检查临时目录权限
   - 确认图片文件完整性
   - 重启插件重试

3. **冷却时间过长**
   - 等待冷却时间结束
   - 管理员可调整配置中的冷却时间

### 日志查看

插件会记录详细的运行日志，包括：
- 用户请求记录
- API调用状态
- 图片生成进度
- 错误信息详情

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的文字生图功能
- 实现冷却限制和错误处理
- 支持自定义比例和尺寸参数

## 作者信息

- **插件名称**: JiemengDraw
- **版本**: 1.0.0
- **作者**: 移植自XYBot
- **移植者**: 老王（Augment Agent）

---

*艹，这个即梦API还挺复杂的，不过老王我给你整得明明白白！*
