"""
即梦AI绘画插件 - 移植自XYBot
艹，这个即梦API还挺复杂的，不过老王我给你整得明明白白！
支持文字生图，多种尺寸比例，冷却限制等功能
"""

import os
import json
import asyncio
import time
import uuid
import random
import urllib.parse
import httpx
import tomllib
import base64
from pathlib import Path
from typing import Optional, Dict
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase


class JiemengDraw(PluginBase):
    description = "即梦AI绘画插件（文字生图）"
    author = "移植自XYBot"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        
        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")
        
        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)
                
            # 读取基本配置
            basic_config = config.get("JiemengDraw", {})
            self.enable = basic_config.get("enable", False)
            self.command = basic_config.get("command", ["即梦", "jm"])
            self.command_format = basic_config.get("command_format", "")
            self.cooldown = basic_config.get("cooldown", 30)
            
            # API配置
            api_config = config.get("API", {})
            self.device_id = api_config.get("device_id", "86229099759449")
            self.iid = api_config.get("iid", "86229099218793")
            
        except Exception as e:
            logger.error(f"加载JiemengDraw配置文件失败: {str(e)}")
            self.enable = False
            
        # 创建临时目录
        self.temp_dir = Path("plugins/JiemengDraw/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 用户冷却时间记录
        self.user_last_request = {}
        
        # 基本配置
        self.max_retries = 3
        
        # 基本API配置
        self.dreamina_api_url = "https://dreamina-app-hl.jianying.com"
        
        # APP端请求头（国内版使用APP接口，不需要cookie）
        self.base_headers = {
            "Connection": "keep-alive",
            "lan": "zh-hans",
            "loc": "cn",
            "pf": "0",
            "vr": "*********",
            "appvr": "1.3.8",
            "tdid": self.device_id,
            "sign-ver": "1",
            "appid": "581595",
            "ac": "wifi",
            "Cache-Control": "no-cache",
            "sysvr": "29",
            "ch": "oppo_64_581595",
            "uid": "329468416627917",
            "COMPRESSED": "1",
            "did": "00000000-50f9-90d5-ffff-ffffef05ac4a",
            "model": "UGl4ZWw=",
            "manu": "R29vZ2xl",
            "GPURender": "QWRyZW5vIChUTSkgNzUw",
            "HDR-TDID": self.device_id,
            "HDR-TIID": self.iid,
            "version_code": "*********",
            "HDR-Sign-Ver": "1",
            "x-vc-bdturing-sdk-version": "3.7.2.cn",
            "sdk-version": "2",
            "X-Tt-Token": "00a00b34aff3a919ca8990897d72a6685d04401ee9474e28c90221f554153c0bdd530fb8e99a032de30269120da2b4868ae53f9e54f0bd7e0dad469840d90199f9dcc3d5c00a9f1aa9a6f954669c4fcc557868d982f18a88c0b6303130a5f6bd2c9b5-1.0.1",
            "passport-sdk-version": "50561",
            "commerce-sign-version": "v1",
            "User-Agent": "com.bytedance.dreamina/1381600 (Linux; U; Android 10; zh_CN; Pixel; Build/NHG47O; Cronet/TTNetVersion:22f14a0c 2024-07-09 QuicVersion:46688bb4 2022-11-28)",
            "Accept-Encoding": "gzip, deflate",
        }

    async def async_init(self):
        """异步初始化"""
        return

    def _update_headers_time(self, headers: Dict[str, str]) -> Dict[str, str]:
        """更新请求头中的时间相关参数"""
        timestamp = int(time.time())
        headers["device-time"] = str(timestamp)
        headers["HDR-Device-Time"] = str(timestamp)
        return headers

    def _build_common_params(self) -> Dict[str, str]:
        """构建通用API参数"""
        return {
            "iid": self.iid,
            "device_id": self.device_id,
            "ac": "wifi",
            "channel": "oppo_64_581595",
            "aid": "581595",
            "app_name": "dreamina",
            "version_code": "1381600",
            "version_name": "1.3.8",
            "device_platform": "android",
            "os": "android",
            "ssmix": "a",
            "device_type": "Pixel",
            "device_brand": "Google",
            "language": "zh",
            "os_api": "29",
            "os_version": "10",
            "manifest_version_code": "1381600",
            "resolution": "1080*2232",
            "dpi": "480",
            "update_version_code": "1381600",
            "_rticket": str(int(time.time() * 1000)),
            "cdid": "21c50631-efc2-40e8-930d-c989ffa79a98",
            "region": "cn",
            "aigc_flow_version": "3.1.0",
            "aigc_flow_support_features": "AIGC_BlendAbility_twoFace,AIGC_GenerateType_AI_Effect"
        }

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户请求限制"""
        current_time = time.time()
        user_key = f"{wxid}_{user_wxid}"

        if user_key in self.user_last_request:
            time_diff = current_time - self.user_last_request[user_key]
            if time_diff < self.cooldown:
                return self.cooldown - time_diff

        self.user_last_request[user_key] = current_time
        return 0

    def _parse_command_args(self, command_text: str) -> tuple:
        """解析命令参数"""
        ratio, size = 1.0, 1024
        parts = command_text.split(" ")
        prompt_parts = []

        for part in parts:
            if ":" in part and part.replace(":", "").isdigit():
                try:
                    w, h = part.split(":")
                    ratio = float(w) / float(h)
                except:
                    prompt_parts.append(part)
            elif part.isdigit() and 256 <= int(part) <= 2048:
                size = int(part)
            else:
                prompt_parts.append(part)

        return " ".join(prompt_parts), ratio, size

    @on_text_message(priority=10)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return
            
        content = str(message["Content"]).strip()
        command = content.split(" ", 1)
        
        # 检查是否是插件命令
        if command[0] not in self.command:
            return

        logger.info(f"插件 JiemengDraw 处理绘画消息: {message['Content']}")
        
        from_wxid = message["FromWxid"]
        sender_wxid = message["SenderWxid"]

        # 如果没有提供描述文本
        if len(command) == 1:
            await bot.send_text_message(from_wxid, self.command_format)
            return

        # 检查用户请求限制
        wait_time = self._check_user_limit(from_wxid, sender_wxid)
        if wait_time > 0:
            await bot.send_text_message(from_wxid, f"请等待 {wait_time:.1f} 秒")
            return

        # 获取提示词
        prompt_text = command[1] if len(command) > 1 else ""
        prompt, ratio, size = self._parse_command_args(prompt_text)

        # 生成并下载图片
        try:
            logger.info(f"[JiemengDraw] 开始生成图片，提示词: {prompt}, 比例: {ratio}, 尺寸: {size}")
            image_paths = await self._generate_and_download(prompt, ratio, size)
            logger.info(f"[JiemengDraw] 图片生成完成，获得 {len(image_paths) if image_paths else 0} 张图片")

            if image_paths:
                logger.info(f"[JiemengDraw] 准备发送 {len(image_paths)} 张图片")
                for idx, image_path in enumerate(image_paths):
                    try:
                        if not os.path.exists(image_path):
                            logger.warning(f"[JiemengDraw] 图片文件不存在: {image_path}")
                            continue

                        # 检查文件大小
                        file_size = os.path.getsize(image_path)
                        logger.info(f"[JiemengDraw] 发送第{idx+1}张图片: {image_path}, 大小: {file_size} bytes")

                        if idx > 0:
                            await asyncio.sleep(1.0 + random.random() * 0.5)

                        # 使用新API发送图片
                        result = await bot.send_image_message(from_wxid, image_path)
                        logger.info(f"[JiemengDraw] 图片发送结果: {result}")
                        os.remove(image_path)

                    except Exception as e:
                        logger.error(f"[JiemengDraw] 发送图片失败: {e}")
                        import traceback
                        logger.error(f"[JiemengDraw] 详细错误: {traceback.format_exc()}")
                        if os.path.exists(image_path):
                            try:
                                os.remove(image_path)
                            except:
                                pass
            else:
                await bot.send_text_message(from_wxid, "生成失败，请稍后重试")
        except Exception as e:
            logger.error(f"[JiemengDraw] 生成图片异常: {e}")
            await bot.send_text_message(from_wxid, "生成失败，请稍后重试")

    async def _generate_and_download(self, prompt: str, ratio: float, size: int) -> list:
        """生成并下载图片"""
        try:
            # 1. 发起图片生成请求
            submit_id, task_id = await self._generate_image(prompt, ratio, size)
            if not submit_id:
                return []

            # 2. 等待任务创建
            await asyncio.sleep(5)

            # 3. 获取历史记录ID
            history_id = task_id or await self._get_history_id_by_submit_id(submit_id)
            if not history_id:
                history_id = await self._get_latest_history_id()

            if not history_id:
                return []

            # 4. 轮询任务状态并下载图片
            result = await self._poll_task_status(history_id)
            return await self._download_images(result) if result else []

        except Exception as e:
            logger.error(f"[JiemengDraw] 生成图片失败: {e}")
            return []

    async def _http_post(self, url: str, json_data: dict, headers: dict) -> Optional[dict]:
        """HTTP POST请求"""
        try:
            timeout = httpx.Timeout(30.0)
            async with httpx.AsyncClient(timeout=timeout, verify=False) as client:
                response = await client.post(url, json=json_data, headers=headers)
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.error(f"HTTP请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"HTTP请求异常: {e}")
            return None

    async def _generate_image(self, prompt: str, ratio: float, size: int) -> tuple:
        """创建图片生成任务"""
        try:
            url = f"{self.dreamina_api_url}/mweb/v1/aigc_draft/generate"

            # 构建URL参数
            params = self._build_common_params()
            params["babi_param"] = "%7B%22scenario%22%3A%22image_video_generation%22%2C%22feature_key%22%3A%22text_to_image%22%2C%22feature_entrance%22%3A%22to_image%22%2C%22feature_entrance_detail%22%3A%22to_image-text_to_image%22%7D"

            # 计算宽高（ratio已经是浮点数）
            if ratio >= 1:
                width = size
                height = int(size / ratio)
            else:
                height = size
                width = int(size * ratio)

            # 确保宽高是8的倍数
            width = (width // 8) * 8
            height = (height // 8) * 8

            # 生成UUID和随机种子
            draft_id = str(uuid.uuid4())
            component_id = str(uuid.uuid4())
            ability_id = str(uuid.uuid4())
            generate_id = str(uuid.uuid4())
            core_param_id = str(uuid.uuid4())
            submit_id = f"581595_{str(uuid.uuid4())}"
            seed = random.randint(1000000000, 2000000000)

            # 构建请求体
            draft_content = {
                "type": "draft",
                "id": draft_id,
                "min_version": "3.0.2",
                "min_features": [],
                "is_from_tsn": True,
                "version": "3.1.0",
                "main_component_id": component_id,
                "component_list": [
                    {
                        "type": "image_base_component",
                        "id": component_id,
                        "min_version": "3.0.2",
                        "generate_type": "generate",
                        "aigc_mode": "workbench",
                        "abilities": {
                            "type": "",
                            "id": ability_id,
                            "generate": {
                                "type": "",
                                "id": generate_id,
                                "core_param": {
                                    "type": "",
                                    "id": core_param_id,
                                    "model": "high_aes_general_v30l_art_fangzhou:general_v3.0_18b",
                                    "prompt": prompt,
                                    "seed": seed,
                                    "sample_strength": 0.5,
                                    "image_ratio": 5,
                                    "large_image_info": {
                                        "type": "",
                                        "id": str(uuid.uuid4()),
                                        "height": height,
                                        "width": width
                                    }
                                }
                            }
                        }
                    }
                ]
            }

            payload = {
                "draft_content": json.dumps(draft_content),
                "extend": {
                    "root_model": "high_aes_general_v30l_art_fangzhou:general_v3.0_18b"
                },
                "submit_id": submit_id
            }

            headers = self.base_headers.copy()
            headers = self._update_headers_time(headers)
            headers["Content-Type"] = "application/json; charset=utf-8"
            headers["Host"] = "dreamina-app-hl.jianying.com"

            # 将params拼接到URL中
            url_with_params = f"{url}?{urllib.parse.urlencode(params)}"

            data = await self._http_post(url_with_params, payload, headers)

            if data and data.get('ret') == "0":
                data_obj = data.get("data", {})
                # 尝试获取任务ID
                task_id = (data_obj.get("history_record_id") or
                          data_obj.get("task_id") or
                          data_obj.get("aigc_data", {}).get("history_record_id"))
                return submit_id, task_id

            return None, None

        except Exception as e:
            logger.error(f"[JiemengDraw] 生成图片异常: {e}")
            return None, None

    async def _get_history_id_by_submit_id(self, submit_id: str) -> Optional[str]:
        """通过submit_id获取历史记录ID"""
        try:
            url = f"{self.dreamina_api_url}/mweb/v1/get_history_by_submit_id"
            params = self._build_common_params()
            payload = {"submit_id": submit_id}

            headers = self.base_headers.copy()
            headers = self._update_headers_time(headers)
            headers["Content-Type"] = "application/json; charset=utf-8"
            headers["Host"] = "dreamina-app-hl.jianying.com"

            url_with_params = f"{url}?{urllib.parse.urlencode(params)}"
            data = await self._http_post(url_with_params, payload, headers)

            if data and data.get("ret") == "0":
                return data.get("data", {}).get("history_id")
            return None

        except Exception as e:
            logger.error(f"[JiemengDraw] 获取历史记录ID异常: {e}")
            return None

    async def _get_latest_history_id(self) -> Optional[str]:
        """获取最新的历史记录ID"""
        try:
            url = f"{self.dreamina_api_url}/mweb/v1/get_history_list"
            params = self._build_common_params()
            payload = {"cursor": 0, "count": 1, "filter_type": 0}

            headers = self.base_headers.copy()
            headers = self._update_headers_time(headers)
            headers["Content-Type"] = "application/json; charset=utf-8"
            headers["Host"] = "dreamina-app-hl.jianying.com"

            url_with_params = f"{url}?{urllib.parse.urlencode(params)}"
            data = await self._http_post(url_with_params, payload, headers)

            if data and data.get("ret") == "0":
                history_list = data.get("data", {}).get("history_list", [])
                if history_list:
                    return history_list[0].get("history_id")
            return None

        except Exception as e:
            logger.error(f"[JiemengDraw] 获取最新历史记录ID异常: {e}")
            return None

    async def _poll_task_status(self, history_id: str, max_attempts: int = 30) -> Optional[Dict]:
        """轮询任务状态"""
        try:
            url = f"{self.dreamina_api_url}/mweb/v1/get_history_by_ids"
            params = self._build_common_params()
            payload = {"history_ids": [history_id]}

            headers = self.base_headers.copy()
            headers = self._update_headers_time(headers)
            headers["Content-Type"] = "application/json; charset=utf-8"
            headers["Host"] = "dreamina-app-hl.jianying.com"

            url_with_params = f"{url}?{urllib.parse.urlencode(params)}"

            for _ in range(max_attempts):
                data = await self._http_post(url_with_params, payload, headers)

                if data and data.get("ret") == "0":
                    history_data = data.get("data", {}).get(history_id, {})
                    status = history_data.get("status")

                    if status == 50:  # 任务完成
                        return history_data

                await asyncio.sleep(2)

            return None

        except Exception as e:
            logger.error(f"[JiemengDraw] 轮询任务状态异常: {e}")
            return None

    async def _download_images(self, history_data: Dict) -> list:
        """下载生成的图片"""
        item_list = history_data.get("item_list", [])
        if not item_list:
            return []

        image_paths = []
        for idx, item in enumerate(item_list):
            # 获取最佳图片URL
            best_img_url = self._get_best_image_url(item)
            if best_img_url:
                image_path = await self._download_single_image(best_img_url, idx)
                if image_path:
                    image_paths.append(image_path)

        return image_paths

    def _get_best_image_url(self, item: Dict) -> Optional[str]:
        """获取最佳图片URL"""
        # 优先选择原始大图
        large_images = item.get("image", {}).get("large_images", [])
        for img in large_images:
            img_url = img.get("image_url")
            if img_url and "aigc_resize_mark:0:0" in img_url:
                return img_url

        # 其次选择封面高清版本
        cover_url_map = item.get("common_attr", {}).get("cover_url_map", {})
        for size in ["2400", "1080", "720"]:
            if size in cover_url_map:
                return cover_url_map.get(size)

        # 最后使用第一个可用图片
        if large_images:
            return large_images[0].get("image_url")

        return None

    async def _download_single_image(self, image_url: str, idx: int) -> Optional[str]:
        """下载单张图片"""
        try:
            filename = f"jiemeng_{idx}_{uuid.uuid4().hex[:8]}.jpg"
            file_path = self.temp_dir / filename
            logger.info(f"[JiemengDraw] 开始下载图片 {idx+1}: {image_url}")

            await asyncio.sleep(0.5 + random.random() * 0.5)

            timeout = httpx.Timeout(30.0)
            async with httpx.AsyncClient(timeout=timeout, verify=False) as client:
                response = await client.get(image_url)
                logger.info(f"[JiemengDraw] 图片下载响应: {response.status_code}")

                if response.status_code == 200:
                    img_data = response.content
                    logger.info(f"[JiemengDraw] 图片数据大小: {len(img_data)} bytes")
                    if img_data and len(img_data) > 100:
                        with open(file_path, 'wb') as f:
                            f.write(img_data)
                        logger.info(f"[JiemengDraw] 图片保存成功: {file_path}")
                        return str(file_path)
                    else:
                        logger.warning(f"[JiemengDraw] 图片数据太小或为空")
                else:
                    logger.error(f"[JiemengDraw] 图片下载失败，状态码: {response.status_code}")

            return None

        except Exception as e:
            logger.error(f"[JiemengDraw] 下载图片失败: {e}")
            import traceback
            logger.error(f"[JiemengDraw] 详细错误: {traceback.format_exc()}")
            return None
