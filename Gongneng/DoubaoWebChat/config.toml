# DoubaoWebChat 配置文件
# 老王专门为你搞的豆包Web对话框配置，别tm乱改！

# 基本配置
[app]
host = "127.0.0.1"
port = 5000
debug = true
secret_key = "doubao_web_chat_secret_key_change_this_in_production"

# 豆包API配置 - 这个cookies必须配置，不然就是个憨批
[doubao_api]
base_url = "https://www.doubao.com/samantha/chat/completion"
device_id = "7468716989062841895"
tea_uuid = "7468716986638386703"
web_id = "7468716986638386703"
# 在这里填入你的豆包cookies，格式就是浏览器F12复制的那一长串
cookies = """gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC45NzI2Mjg1OTg5NDU1MDY5fQ==.MZKIgojxOPibWAvigoCFZXa05rnrFeMkYEm525CD9n0=; i18next=zh; _ga=GA1.1.1580645520.1754128762; flow_user_country=CN; s_v_web_id=verify_mdu2z8hv_WNml3NSr_U6wY_4x12_8LPw_VKeRv0cwIhUi; passport_csrf_token=05e69445ad4e580ec21994fe6fb36b5e; passport_csrf_token_default=05e69445ad4e580ec21994fe6fb36b5e; ttcid=b9f5f489ee264b62b2a1d0f4aaf611bb17; odin_tt=582f81ff63fd592f0a89dd7218a889805e9f4a905a93408ddd05fd76629d170091edc28d6a2c0f9a1bf0fe63fb5f39c61185719c97a34b6b5f6d50e1a9a76952; n_mh=VhSvSe1Kqdv21URHsx7mhxnio1YOjcYFGu1rQI2CPxc; sid_guard=638806880f004016e10f441cb7996f71%7C1754128784%7C5184000%7CWed%2C+01-Oct-2025+09%3A59%3A44+GMT; uid_tt=07216e6e52ec12b8057149492f56bead; uid_tt_ss=07216e6e52ec12b8057149492f56bead; sid_tt=638806880f004016e10f441cb7996f71; sessionid=638806880f004016e10f441cb7996f71; sessionid_ss=638806880f004016e10f441cb7996f71; session_tlb_tag=sttt%7C16%7CY4gGiA8AQBbhD0Qct5lvcf_________m2jciQu-zFLfVIQH92T4nBkcrUeaaLOCHJdtAheV8Dxg%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KDZjNmQwZjM0MmU4ZTAzYjhhMzdlNjc4ZWI1Y2U1MDFhZTQ4OWQwZTcKHwjkxID8tsxzEJDDt8QGGMKxHiAMMN_w27kGOAdA9AcaAmhsIiA2Mzg4MDY4ODBmMDA0MDE2ZTEwZjQ0MWNiNzk5NmY3MQ; ssid_ucp_v1=1.0.0-KDZjNmQwZjM0MmU4ZTAzYjhhMzdlNjc4ZWI1Y2U1MDFhZTQ4OWQwZTcKHwjkxID8tsxzEJDDt8QGGMKxHiAMMN_w27kGOAdA9AcaAmhsIiA2Mzg4MDY4ODBmMDA0MDE2ZTEwZjQ0MWNiNzk5NmY3MQ; flow_ssr_sidebar_expand=1; ttwid=1%7C076PnuiA15wnNnBRo9yafrg1AeNCK1F6QSgof1_d9B0%7C1754128785%7C6558947c822b16a9224c220348d46ab56f88a3db796f03eafc34e7d29bc62bf0; _ga_G8EP5CG8VZ=GS2.1.s1754128761$o1$g1$t1754128788$j33$l0$h0; passport_fe_beating_status=true; msToken=RzLWA0eUsbGLDMhPlFh7T9ghSBRT3hRQ0kviB3lIYrP8KpuuTXV5nfDiet9v2aRM2UI_1or-zmIkrLc3GijRWQ8HyZVU8hMYKspN0yCBH1LUaAPQaURNTA==; tt_scid=yXnwPd0A3EyBfz6cKbHbHZhsAPGZPYFNN1vbypAZ9m1KNl4ddSw66EajB6ybjuUe8ab6"""

# 请求限制配置 - 防止你这个憨批疯狂刷API
[rate_limit]
requests_per_minute = 30  # 每分钟最多30次请求
min_request_interval = 2.0  # 最小请求间隔（秒）
timeout_connect = 15.0  # 连接超时（增加到15秒）
timeout_read = 300.0  # 读取超时
timeout_write = 60.0  # 写入超时
max_connections = 50  # 最大连接数
max_keepalive_connections = 20  # 最大保持连接数
keepalive_expiry = 30.0  # 连接保持时间（秒）

# 会话管理配置
[session]
max_history_messages = 50  # 最大历史消息数量
session_timeout = 1800  # 会话超时时间（秒），30分钟
auto_clear_history = true  # 是否自动清理历史记录

# 界面配置 - 让你的界面不那么丑
[ui]
theme = "dark"  # 主题：dark(深色) 或 light(浅色)
title = "豆包AI对话框 - 老王出品"
welcome_message = "欢迎使用豆包AI对话框！直接输入消息开始对话吧，别客气！"
placeholder_text = "在这里输入你的问题..."
max_input_length = 2000  # 最大输入长度

# 系统提示词配置 - 可以自定义豆包的行为
[system_prompt]
enabled = false  # 是否启用自定义系统提示词
content = "你是一个有用的AI助手，请用简洁明了的方式回答用户问题。"

# 高级配置 - 别乱动，除非你知道自己在干什么
[advanced]
enable_streaming = true  # 是否启用流式响应
enable_markdown = true  # 是否启用Markdown渲染
enable_code_highlight = true  # 是否启用代码高亮
auto_scroll = true  # 是否自动滚动到底部
show_typing_indicator = true  # 是否显示正在输入指示器

# 日志配置
[logging]
level = "INFO"  # 日志级别：DEBUG, INFO, WARNING, ERROR
log_file = "doubao_web_chat.log"  # 日志文件名
max_log_size = "10MB"  # 最大日志文件大小
backup_count = 5  # 日志文件备份数量
